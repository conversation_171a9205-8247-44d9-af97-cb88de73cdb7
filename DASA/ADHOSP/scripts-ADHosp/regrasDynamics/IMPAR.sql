-- ========================================
-- Usuario do ADHosp
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND (ISNULL(UID_FirmPartner.Name2, '') = 'IMPAR')
AND (
        -- inclui apenas colaboradores que entraram APÓS a data de aplicação
        ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') > '2025-08-20'
    )

-- ========================================
-- Usuario do AD com conta T ADHosp
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 1)
AND (IdentityType = N'Primary')
AND (isinactive = '0')
AND (ISNULL(UID_FirmPartner.Name2, '') = 'IMPAR') --!!!!!!!Corrigir para gerente do terceiro!!!!!!!
AND (
        -- inclui apenas colaboradores que entraram APÓS a data de aplicação
        ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') > '2025-08-20'
    )
