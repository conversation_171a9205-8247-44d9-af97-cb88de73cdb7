{"info": {"_postman_id": "519568fb-23d0-4b60-a9da-fca502cc3b36", "name": "Revvo", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "36547434"}, "item": [{"name": "HML", "item": [{"name": "DASAFunc", "item": [{"name": "gerar token", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4a44dc15364204a80fe80e9039455cc1\",\n  \"client_secret\": \"687f9cd069ecf36913f906839f1bbca253fa617323c3d\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://universidadedasa-homolog.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["universidadedasa-homolog", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://universidadedasa-homolog.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "tokenName", "value": "DASAFunc", "type": "string"}, {"key": "clientSecret", "value": "687f9cd069ecf36913f906839f1bbca253fa617323c3d", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user\":\n    {\n        \"customfields\": [\n        {\n          \"type\": \"denominacao_do_grupo_de_empregados\",\n          \"value\": \"Op. Técnicos\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://universidadedasa-homolog.learningflix.net/webservice/api/v3/users/953", "protocol": "https", "host": ["universidadedasa-homolog", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "953"], "query": [{"key": "username[like]", "value": "*32577513895*", "disabled": true}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}, {"name": "AmericasFunc", "item": [{"name": "gerar token Copy", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"asd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://academiamericas-homolog.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["academiamericas-homolog", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários Copy", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://academiamericas-homolog.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "tokenName", "value": "AmericasFunc", "type": "string"}, {"key": "clientSecret", "value": "687f9b9d22082cd15882348e71fae671474070d7c9ccd", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user\":\n    {\n      \"customfields\": [\n        {\n          \"type\": \"denominacao_do_subgrupo_de_empregados\",\n          \"value\": \"Técnico\"\n        },\n        {\n          \"type\": \"data_de_nascimento\",\n          \"value\": \"650073600\"\n        },\n        {\n          \"type\": \"descricao_de_cargo\",\n          \"value\": \"TECNICO ENFERMAGEM\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://academiamericas-homolog.learningflix.net/webservice/api/v3/users/143231", "protocol": "https", "host": ["academiamericas-homolog", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "143231"]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "<PERSON>ar <PERSON>", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}, {"name": "AmericasMed", "item": [{"name": "gerar token Copy 2", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"asd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários Copy 2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://academiaamericas-homolog.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "687f9be816c212a57a5e3cf67ffeb536648b6abb44d3b", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "tokenName", "value": "AmericasMed", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user\":\n    {\n      \"customfields\": [\n        {\n          \"type\": \"denominacao_do_subgrupo_de_empregados\",\n          \"value\": \"Assistente\"\n        },\n        {\n          \"type\": \"data_de_nascimento\",\n          \"value\": \"20\"\n        },\n        {\n          \"type\": \"descricao_de_cargo\",\n          \"value\": \"ASSISTENTE ADMINISTRATIVO\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://academiaamericas-homolog.learningflix.net/webservice/api/v3/users/20", "protocol": "https", "host": ["academiaamericas-homolog", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "20"]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "Listar Cursos Copy 2", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}]}, {"name": "PRD", "item": [{"name": "DASAFunc Copy", "item": [{"name": "gerar token", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4a44dc15364204a80fe80e9039455cc1\",\n  \"client_secret\": \"687f9cd069ecf36913f906839f1bbca253fa617323c3d\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://universidadedasa-homolog.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["universidadedasa-homolog", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://universidadedasa.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "tokenName", "value": "DASAFunc", "type": "string"}, {"key": "clientSecret", "value": "687f9cd069ecf36913f906839f1bbca253fa617323c3d", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user\":\n    {\n        \"customfields\": [\n        {\n          \"type\": \"denominacao_do_grupo_de_empregados\",\n          \"value\": \"Op. Técnicos\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://universidadedasa.learningflix.net/webservice/api/v3/users/953", "protocol": "https", "host": ["universidadedasa", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "953"], "query": [{"key": "username[like]", "value": "*32577513895*", "disabled": true}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}, {"name": "AmericasFunc Copy", "item": [{"name": "gerar token Copy", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"asd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários Copy", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://academiamericas.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "tokenName", "value": "AmericasFunc", "type": "string"}, {"key": "clientSecret", "value": "687f9b9d22082cd15882348e71fae671474070d7c9ccd", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user\":\n    {\n      \"customfields\": [\n        {\n          \"type\": \"denominacao_do_subgrupo_de_empregados\",\n          \"value\": \"Técnico\"\n        },\n        {\n          \"type\": \"data_de_nascimento\",\n          \"value\": \"650073600\"\n        },\n        {\n          \"type\": \"descricao_de_cargo\",\n          \"value\": \"TECNICO ENFERMAGEM\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://academiamericas.learningflix.net/webservice/api/v3/users/143231", "protocol": "https", "host": ["academiamericas", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "143231"]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "<PERSON>ar <PERSON>", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}, {"name": "AmericasMed Copy", "item": [{"name": "gerar token Copy 2", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"asd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários Copy 2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://academiaamericas.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "687f9be816c212a57a5e3cf67ffeb536648b6abb44d3b", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "tokenName", "value": "AmericasMed", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user\":\n    {\n      \"customfields\": [\n        {\n          \"type\": \"denominacao_do_subgrupo_de_empregados\",\n          \"value\": \"Assistente\"\n        },\n        {\n          \"type\": \"data_de_nascimento\",\n          \"value\": \"20\"\n        },\n        {\n          \"type\": \"descricao_de_cargo\",\n          \"value\": \"ASSISTENTE ADMINISTRATIVO\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://academiaamericas.learningflix.net/webservice/api/v3/users/20", "protocol": "https", "host": ["academiaamericas", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "20"]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "Listar Cursos Copy 2", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}, {"name": "DASAFunc Copy", "item": [{"name": "gerar token", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4a44dc15364204a80fe80e9039455cc1\",\n  \"client_secret\": \"687f9cd069ecf36913f906839f1bbca253fa617323c3d\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://universidadedasa-homolog.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["universidadedasa-homolog", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://universidadedasa.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "tokenName", "value": "DASAFunc", "type": "string"}, {"key": "clientSecret", "value": "687f9cd069ecf36913f906839f1bbca253fa617323c3d", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user\":\n    {\n        \"customfields\": [\n        {\n          \"type\": \"denominacao_do_grupo_de_empregados\",\n          \"value\": \"Op. Técnicos\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://universidadedasa.learningflix.net/webservice/api/v3/users/953", "protocol": "https", "host": ["universidadedasa", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "953"], "query": [{"key": "username[like]", "value": "*32577513895*", "disabled": true}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}, {"name": "AmericasFunc Copy", "item": [{"name": "gerar token Copy", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"asd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários Copy", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://academiamericas.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "tokenName", "value": "AmericasFunc", "type": "string"}, {"key": "clientSecret", "value": "687f9b9d22082cd15882348e71fae671474070d7c9ccd", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user\":\n    {\n      \"customfields\": [\n        {\n          \"type\": \"denominacao_do_subgrupo_de_empregados\",\n          \"value\": \"Técnico\"\n        },\n        {\n          \"type\": \"data_de_nascimento\",\n          \"value\": \"650073600\"\n        },\n        {\n          \"type\": \"descricao_de_cargo\",\n          \"value\": \"TECNICO ENFERMAGEM\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://academiamericas.learningflix.net/webservice/api/v3/users/143231", "protocol": "https", "host": ["academiamericas", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "143231"]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "<PERSON>ar <PERSON>", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}, {"name": "AmericasMed Copy", "item": [{"name": "gerar token Copy 2", "request": {"method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"asd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/oauth2/token", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "oauth2", "token"]}, "description": "Generated from cURL: curl -X 'POST' \\\n  'https://dasa-poc.learningflix.net/webservice/api/oauth2/token' \\\n  -H 'accept: application/json' \\\n  -H 'Content-Type: application/json' \\\n  -d '{\n  \"grant_type\": \"client_credentials\",\n  \"client_id\": \"4ec9599fc203d176a301536c2e091a19\",\n  \"client_secret\": \"6846ee05a7f89b456d5a46256704e9b502c7406f0b172\"\n}'\n"}, "response": []}, {"name": "Listar Usuários Copy 2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://academiaamericas.learningflix.net/webservice/api/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "687f9be816c212a57a5e3cf67ffeb536648b6abb44d3b", "type": "string"}, {"key": "clientId", "value": "4a44dc15364204a80fe80e9039455cc1", "type": "string"}, {"key": "tokenName", "value": "AmericasMed", "type": "string"}, {"key": "refreshTokenUrl", "value": "", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user\":\n    {\n      \"customfields\": [\n        {\n          \"type\": \"denominacao_do_subgrupo_de_empregados\",\n          \"value\": \"Assistente\"\n        },\n        {\n          \"type\": \"data_de_nascimento\",\n          \"value\": \"20\"\n        },\n        {\n          \"type\": \"descricao_de_cargo\",\n          \"value\": \"ASSISTENTE ADMINISTRATIVO\"\n        }\n      ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://academiaamericas.learningflix.net/webservice/api/v3/users/20", "protocol": "https", "host": ["academiaamericas", "learningflix", "net"], "path": ["webservice", "api", "v3", "users", "20"]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/users?profile_field_%7Bname%7D=profile_field_department[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}, {"name": "Listar Cursos Copy 2", "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50", "protocol": "https", "host": ["dasa-poc", "learningflix", "net"], "path": ["webservice", "api", "v3", "courses"], "query": [{"key": "custom_field_%7Bname%7D", "value": "custom_field_audience[eq]=TI"}, {"key": "_limit", "value": "50"}]}, "description": "Generated from cURL: curl -X 'GET' \\\n  'https://dasa-poc.learningflix.net/webservice/api/v3/courses?custom_field_%7Bname%7D=custom_field_audience[eq]=TI&_limit=50' \\\n  -H 'accept: application/json'"}, "response": []}]}]}]}