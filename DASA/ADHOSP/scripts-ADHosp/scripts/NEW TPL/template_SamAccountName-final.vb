' ========================================
' Template de SAMAccountName - Segmentação DASA/IMPAR
' Autor: Sistema ADHosp
' Data: 2025-08-20
' Descrição: Gera SAMAccountName baseado na empresa e tipo de colaborador
' Regras:
' - Médico IMPAR (credencial M*): M + CPF
' - Interno DASA: F + CPF
' - Interno IMPAR: J + CPF
' - Externo DASA/IMPAR: T + CPF
' ========================================

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	Dim logMessage As String = ""
	Dim Acc As String = ""
	Dim MyString As String = ""
	Dim i As Integer = 0
	Dim AccExist As Boolean = False
	Dim txt As New System.Text.StringBuilder(Acc.Length)
	Dim f As ISqlFormatter = Connection.SqlFormatter
	Dim prefixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Prefix")
	Dim postfixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Postfix")
	Dim empresa As String = $FK(UID_Person).Ident_Domain$
	Dim tipoColaborador As String = $FK(UID_Person).IsExternal$
	Dim credencial As String = $FK(UID_Person).CentralAccount$
	Dim cpf As String = $FK(UID_Person).CustomProperty02$
	Dim dominio As String = $FK(UID_ADSDomain).Ident_Domain$

	logMessage = String.Format("[{0}] Iniciando geração de SAMAccountName", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
	Console.WriteLine(logMessage)

	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
			'only initially and if the operator has not set the SAMAccountName manually
			If Not $[IsLoaded]:Bool$ AndAlso $cn$<>"" AndAlso Not Entity.Columns.Item("SAMAccountName").ChangeLevels.Get(0) Then
				If $IsPrivilegedAccount:Bool$ Then
					Acc = Left(prefixAccount & Left($cn$, 18-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 18)
				Else
					Acc = Left($cn$, 18)
				End If
			Else
				Acc = CStr(Value)
			End If
		Case -1:'fill property initially depending on the employee
			If Not $[IsLoaded]:Bool$ Then
				' Implementação da lógica de segmentação de SAMAccountName
				Acc = GerarSAMAccountNameSegmentado(empresa, tipoColaborador, credencial, cpf, dominio)
			Else
				Acc = CStr(Value)
			End If
		Case 1:'update property depending on the employee
			' Implementação da lógica de segmentação de SAMAccountName
			Acc = GerarSAMAccountNameSegmentado(empresa, tipoColaborador, credencial, cpf, dominio)
	End Select

	If Acc <> "" Then
		' Invalid characters in Samaccountname are replaced with _
		' /\[]:;|=,+*?<>"@
		For Each c As Char In Acc
			Select Case c
				Case "/"c, "\"c, "["c, "]"c, ":"c, ";"c, "|"c, "="c, ","c, "+"c, "*"c, "?"c, "<"c, ">"c, """"c, "@"c
					txt.Append("_"c)
				Case Else
					txt.Append(c)
			End Select
		Next
		Acc = txt.ToString()
		
		'character . at the end of Samaccountname is invalid
		If Right(Acc,1)="."c Then
			Acc = Acc.Substring(0,Acc.Length-1)
		End If
		
		'prevents triggering the template on changes to the ADSDomain (use ObjectWalker)
		Dim Domain As String = GetValue("UID_ADSDomain").String
			
		Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
			Case 0:'do not get data from employee (if this account is already in the domain then increment)
				Do While Connection.Exists("ADSAccount", f.AndRelation(f.Comparison("SAMAccountName", Acc, ValType.String, CompareOperator.Equal), _
					f.UidComparison("UID_ADSDomain", Domain), _
					f.UidComparison("UID_ADSAccount", $UID_ADSAccount$, CompareOperator.NotEqual)))
					i = i + 1
					Acc = txt.ToString() & i.ToString()
				Loop
		End Select

	End If
	
	Value = Acc

End If

' ========================================
' Função para gerar SAMAccountName baseado na segmentação por empresa
' ========================================
Public Function GerarSAMAccountNameSegmentado(empresa As String, tipoColaborador As String, credencial As String, cpf As String, dominio As String) As String
	Dim logMessage As String = ""
	Dim resultado As String = ""

	Try
		logMessage = String.Format("[{0}] Iniciando geração segmentada de SAMAccountName", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
		Console.WriteLine(logMessage)

		' Obter informações da pessoa
		logMessage = String.Format("[{0}] Credencial obtida: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), credencial)
		Console.WriteLine(logMessage)

		' Obter CPF (assumindo que está em um campo específico)
		Try
			logMessage = String.Format("[{0}] Obtendo CPF...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
			Console.WriteLine(logMessage)

			logMessage = String.Format("[{0}] CPF obtido de CustomProperty02: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), cpf)
			Console.WriteLine(logMessage)

			' Limpar formatação do CPF (manter apenas números)
			Dim cpfOriginal As String = cpf
			cpf = System.Text.RegularExpressions.Regex.Replace(cpf, "[^0-9]", "")

			If cpfOriginal <> cpf Then
				logMessage = String.Format("[{0}] CPF formatado: {1} -> {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), cpfOriginal, cpf)
				Console.WriteLine(logMessage)
			End If

		Catch cpfEx As Exception
			logMessage = String.Format("[{0}] ERRO ao obter CPF: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), cpfEx.Message)
			Console.WriteLine(logMessage)
			cpf = ""
		End Try

		' Obter tipo de colaborador (interno/externo)
		Try
			logMessage = String.Format("[{0}] Obtendo tipo de colaborador...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
			Console.WriteLine(logMessage)

			' Assumindo que existe um campo que identifica se é interno ou externo
			' Ajustar conforme a estrutura real do sistema
			If String.IsNullOrEmpty(tipoColab) Then
				' Fallback: verificar se é conta privilegiada para determinar tipo
				If $tipoColab:Bool$ Then
					tipoColaborador = "externo"
					logMessage = String.Format("[{0}] Tipo inferido por conta de terceiro: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), tipoColaborador)
					Console.WriteLine(logMessage)
				Else
					tipoColaborador = "interno"
					logMessage = String.Format("[{0}] Tipo inferido por conta de interno: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), tipoColaborador)
					Console.WriteLine(logMessage)
				End If
			Else
				tipoColaborador = tipoColab.ToLower()
				logMessage = String.Format("[{0}] Tipo obtido do campo IsExternal: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), tipoColaborador)
				Console.WriteLine(logMessage)
			End If
		Catch tipoEx As Exception
			logMessage = String.Format("[{0}] ERRO ao obter tipo de colaborador: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), tipoEx.Message)
			Console.WriteLine(logMessage)
			' Fallback padrão
			tipoColaborador = "interno"
		End Try

		' Aplicar regras de segmentação
		logMessage = String.Format("[{0}] Aplicando regras de segmentação - Empresa: {1}, Tipo: {2}, Credencial: {3}, CPF: {4}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), empresa, tipoColaborador, credencial, cpf)
		Console.WriteLine(logMessage)

		If tipoColaborador = "interno" AndAlso dominio = "DASA" Then
			' Se for interno no domínio DASA → F + CPF
			resultado = "F" + cpf

			logMessage = String.Format("[{0}] Regra aplicada: Médico IMPAR - SAM: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultado)
			Console.WriteLine(logMessage)

		ElseIf tipoColaborador = "interno" AndAlso dominio = "adhosp" Then
			' Se for interno da IMPAR → J + CPF
			resultado = "J" + cpf

			logMessage = String.Format("[{0}] Regra aplicada: Interno DASA - SAM: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultado)
			Console.WriteLine(logMessage)

		Else
			' Fallback para casos não cobertos pelas regras
			logMessage = String.Format("[{0}] Aplicando regra fallback - nenhuma regra específica atendida", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
			Console.WriteLine(logMessage)

			If $IsPrivilegedAccount:Bool$ Then
				Dim prefixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Prefix")
				Dim postfixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Postfix")
				MyString = credencial
				resultado = Left(prefixAccount & Left(MyString.TrimStart("F"c,"T"c), 20-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 20)

				logMessage = String.Format("[{0}] Fallback conta privilegiada - SAM: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultado)
				Console.WriteLine(logMessage)
			Else
				resultado = Left(credencial, 20)

				logMessage = String.Format("[{0}] Fallback conta padrão - SAM: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultado)
				Console.WriteLine(logMessage)
			End If
		End If

		' Limitar tamanho do resultado
		If resultado.Length > 20 Then
			Dim resultadoOriginal As String = resultado
			resultado = Left(resultado, 20)

			logMessage = String.Format("[{0}] SAMAccountName truncado por tamanho: {1} -> {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultadoOriginal, resultado)
			Console.WriteLine(logMessage)
		End If

		logMessage = String.Format("[{0}] SAMAccountName final gerado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultado)
		Console.WriteLine(logMessage)

	Catch ex As Exception
		logMessage = String.Format("[{0}] ERRO CRÍTICO na geração segmentada: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
		Console.WriteLine(logMessage)

		' Em caso de erro, usar lógica original
		logMessage = String.Format("[{0}] Aplicando lógica original como fallback", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
		Console.WriteLine(logMessage)

		Try
			If $IsPrivilegedAccount:Bool$ Then
				Dim prefixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Prefix")
				Dim postfixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Postfix")
				MyString = $FK(UID_Person).CentralAccount$
				resultado = Left(prefixAccount & Left(MyString.TrimStart("F"c,"T"c), 20-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 20)

				logMessage = String.Format("[{0}] Fallback conta privilegiada aplicado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultado)
				Console.WriteLine(logMessage)
			Else
				resultado = Left($FK(UID_Person).CentralAccount$, 20)

				logMessage = String.Format("[{0}] Fallback conta padrão aplicado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), resultado)
				Console.WriteLine(logMessage)
			End If
		Catch fallbackEx As Exception
			logMessage = String.Format("[{0}] ERRO CRÍTICO no fallback: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), fallbackEx.Message)
			Console.WriteLine(logMessage)
			resultado = "ERROR_SAM"
		End Try
	End Try

	Return resultado
End Function