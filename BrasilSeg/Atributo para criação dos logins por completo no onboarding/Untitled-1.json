{"id": "c2c1ca6da17d495a8bbf855f88377536", "name": "SAP - PACOTE BÁSICO BSG", "created": "2024-04-04T17:59:39.381792Z", "modified": "2025-08-12T17:14:47.153759Z", "description": null, "owner": {"type": "IDENTITY", "id": "a185983bcb864702849479cb7eb4768c", "name": "Thiago.Caires"}, "entitlements": [], "accessProfiles": [], "membership": {"type": "STANDARD", "criteria": {"operation": "AND", "key": null, "values": null, "stringValue": null, "children": [{"operation": "NOT_EQUALS", "key": {"type": "IDENTITY", "property": "attribute.cloudLifecycleState", "sourceId": null}, "values": ["disable"], "stringValue": null, "children": null}, {"operation": "NOT_EQUALS", "key": {"type": "IDENTITY", "property": "attribute.login", "sourceId": null}, "values": ["NONE"], "stringValue": null, "children": null}, {"operation": "EQUALS", "key": {"type": "IDENTITY", "property": "attribute.isadmin", "sourceId": null}, "values": ["false"], "stringValue": null, "children": null}, {"operation": "NOT_EQUALS", "key": {"type": "IDENTITY", "property": "attribute.displayName", "sourceId": null}, "values": ["SailPoint Services"], "stringValue": null, "children": null}, {"operation": "NOT_EQUALS", "key": {"type": "IDENTITY", "property": "attribute.displayName", "sourceId": null}, "values": ["SailPoint Support"], "stringValue": null, "children": null}, {"operation": "NOT_EQUALS", "key": {"type": "IDENTITY", "property": "attribute.cloudLifecycleState", "sourceId": null}, "values": ["consultadebito"], "stringValue": null, "children": null}, {"operation": "NOT_EQUALS", "key": {"type": "IDENTITY", "property": "attribute.cloudLifecycleState", "sourceId": null}, "values": ["inactive"], "stringValue": null, "children": null}, {"operation": "OR", "key": null, "values": null, "stringValue": null, "children": [{"operation": "EQUALS", "key": {"type": "IDENTITY", "property": "attribute.tipoDeConta", "sourceId": null}, "values": ["F"], "stringValue": null, "children": null}, {"operation": "EQUALS", "key": {"type": "IDENTITY", "property": "attribute.tipoDeConta", "sourceId": null}, "values": ["C"], "stringValue": null, "children": null}]}, {"operation": "EQUALS", "key": {"type": "IDENTITY", "property": "attribute.existecontaadsap", "sourceId": null}, "values": ["true"], "stringValue": null, "children": null}, {"operation": "OR", "key": null, "values": null, "stringValue": null, "children": [{"operation": "NOT_EQUALS", "key": {"type": "ACCOUNT", "property": "attribute.TIPO_ACESSO", "sourceId": "2c91808474fee29c017504aa19a75f3e"}, "values": ["2"], "stringValue": null, "children": null}, {"operation": "EQUALS", "key": {"type": "ACCOUNT", "property": "attribute.TIPO_ACESSO", "sourceId": "2c91808474fee29c017504aa19a75f3e"}, "values": ["1"], "stringValue": null, "children": null}]}]}, "identities": null}, "legacyMembershipInfo": null, "enabled": true, "requestable": true, "accessRequestConfig": {"commentsRequired": true, "denialCommentsRequired": false, "approvalSchemes": [{"approverType": "OWNER", "approverId": null}], "reauthorizationRequired": false}, "revocationRequestConfig": {"approvalSchemes": [{"approverType": "OWNER", "approverId": null}]}, "segments": [], "dimensional": false, "dimensionRefs": [], "accessModelMetadata": {"attributes": []}, "additionalOwners": []}