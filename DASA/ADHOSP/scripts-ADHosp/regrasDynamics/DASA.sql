-- ========================================
-- Usuario do AD
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND ISNULL(companymember, 'xx') NOT IN 
    (
        'HOSPITAL SÃO LUCAS',
        'MATERNIDADE BRASÍLIA',
        'HOSPITAL BRASÍLIA',
        'HOSPITAL ÁGUAS CLARA',
        'HOSPITAL SANTA PAULA',
        'H9J',
        'CHN',
        'SANTA CELINA GI',
        'SAÚDE CELINA',
        'INNOVA'
    )

-- ========================================
-- Usuario do AD com conta T
-- ========================================

(
    -- Verifica se a data de entrada é válida (não nula e menor/igual à data atual)
    -- OU se existe uma conta AD associada à pessoa
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        -- Verifica se existe pelo menos uma conta AD para esta pessoa
        EXISTS
        (
            SELECT 1
            FROM (
                -- Busca todas as pessoas que possuem conta no Active Directory
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1  -- Condição sempre verdadeira (busca todos os registros)
            ) AS X
            WHERE X.UID_Person = Person.UID_Person  -- Relaciona com a pessoa atual
        )
    )
)
AND (isexternal = 1)  -- Filtra apenas usuários externos (não funcionários internos)
AND (IdentityType = N'Primary')  -- Considera apenas identidades primárias (não secundárias)
AND (isinactive = '0')  -- Exclui usuários inativos (apenas usuários ativos)

-- ========================================
-- Usuario do AD com conta T
-- ========================================

(
    -- Verifica se a data de entrada é válida (não nula e menor/igual à data atual)
    -- OU se existe uma conta AD associada à pessoa
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        -- Verifica se existe pelo menos uma conta AD para esta pessoa
        EXISTS
        (
            SELECT 1
            FROM (
                -- Busca todas as pessoas que possuem conta no Active Directory
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1  -- Condição sempre verdadeira (busca todos os registros)
            ) AS X
            WHERE X.UID_Person = Person.UID_Person  -- Relaciona com a pessoa atual
        )
    )
)
AND (isexternal = 1)  -- Filtra apenas usuários externos (não funcionários internos)
AND (IdentityType = N'Primary')  -- Considera apenas identidades primárias (não secundárias)
AND (isinactive = '0')  -- Exclui usuários inativos (apenas usuários ativos)
AND NOT (UID_FirmPartner = 'xxxxxxxxxxxxxxx')  -- Exclui parceiro específico da empresa

-- ========================================
-- Usuario AD Hospitais
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND ISNULL(companymember, 'xx') IN (
    'HOSPITAL SÃO LUCAS',
    'MATERNIDADE BRASÍLIA',
    'HOSPITAL BRASÍLIA',
    'HOSPITAL ÁGUAS CLARA',
    'HOSPITAL SANTA PAULA',
    'H9J',
    'IMPAR',
    'CHN',
    'INNOVA'
)

-- ========================================
-- Usuario AD CIIA
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND ISNULL(companymember, 'xx') IN (
    'SANTA CELINA GI',
    'SAÚDE CELINA'
)

-- ========================================
-- Usuario do AD com conta T - Nova Regra
-- ========================================

(isnull(cast(EntryDate as date), '1899-12-30 00:00:00.000') <= cast(GETDATE() as date) OR ( EXISTS ( SELECT 1 FROM  (SELECT UID_Person FROM ADSAccount WHERE 1 = 1) as X  WHERE X.UID_Person = Person.UID_Person  ) )) and (isexternal = 1) and (IdentityType = N'Primary') and (isinactive = '0') AND (
	    -- Nova condição: inclui apenas colaboradores que entraram após a data de aplicação da regra
	    -- Substitua 'YYYY-MM-DD' pela data real de aplicação da regra
	    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') < '2025-08-20')
		OR
		(
	    -- Verifica se a data de entrada é válida (não nula e menor/igual à data atual)
	    -- OU se existe uma conta AD associada à pessoa
	    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
	    OR
	    (
	        -- Verifica se existe pelo menos uma conta AD para esta pessoa
	        EXISTS
	        (
	            SELECT 1
	            FROM (
	                -- Busca todas as pessoas que possuem conta no Active Directory
	                SELECT UID_Person
	                FROM ADSAccount
	                WHERE 1 = 1  -- Condição sempre verdadeira (busca todos os registros)
	            ) AS X
	            WHERE X.UID_Person = Person.UID_Person  -- Relaciona com a pessoa atual
	        )
	    )
	)
	AND (isexternal = 1)  -- Filtra apenas usuários externos (não funcionários internos)
	AND (IdentityType = N'Primary')  -- Considera apenas identidades primárias (não secundárias)
	AND (isinactive = '0')  -- Exclui usuários inativos (apenas usuários ativos)
	AND (ISNULL(UID_FirmPartner, '') <> '914e450a-2ed3-42e8-930c-57d2b89598fa')  -- Exclui parceiro específico da empresa
	AND (
	    -- Nova condição: inclui apenas colaboradores que entraram após a data de aplicação da regra
	    -- Substitua 'YYYY-MM-DD' pela data real de aplicação da regra
	    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') > '2025-08-20')

-- ========================================
-- Usuario do AD com conta T - Nova Regra - Ambientalizada
-- ========================================

(
    -- CONDIÇÃO PRINCIPAL: Validação de data de entrada OU existência de conta AD
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        -- Verifica se existe conta no Active Directory para esta pessoa
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1  -- Busca todos os registros de contas AD
            ) AS X
            WHERE X.UID_Person = Person.UID_Person  -- Relaciona com a pessoa atual
        )
    )
)
AND (isexternal = 1)  -- FILTRO: Apenas usuários externos (terceirizados/consultores)
AND (IdentityType = N'Primary')  -- FILTRO: Apenas identidades primárias (não secundárias)
AND (isinactive = '0')  -- FILTRO: Apenas usuários ativos (exclui inativos)
AND
(
    -- REGRA TEMPORAL - PARTE 1: Usuários que entraram ANTES da data de aplicação da regra
    -- Inclui colaboradores antigos (entrada anterior a 2025-08-20)
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') < '2025-08-25'

    OR

    (
        -- REGRA TEMPORAL - PARTE 2: Usuários que entraram APÓS a data de aplicação da regra
        -- Para novos colaboradores (entrada posterior a 2025-08-20), aplicam-se validações adicionais

        -- Validação de data de entrada OU existência de conta AD
        ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
        OR
        (
            -- Verifica se existe pelo menos uma conta AD para esta pessoa
            EXISTS
            (
                SELECT 1
                FROM (
                    -- Busca todas as pessoas que possuem conta no Active Directory
                    SELECT UID_Person
                    FROM ADSAccount
                    WHERE 1 = 1  -- Condição sempre verdadeira (busca todos os registros)
                ) AS X
                WHERE X.UID_Person = Person.UID_Person  -- Relaciona com a pessoa atual
            )
        )

        AND (isexternal = 1)  -- Reforça: apenas usuários externos
        AND (IdentityType = N'Primary')  -- Reforça: apenas identidades primárias
        AND (isinactive = '0')  -- Reforça: apenas usuários ativos
        AND (ISNULL(UID_FirmPartner, '') <> '914e450a-2ed3-42e8-930c-57d2b89598fa')  -- Exclui parceiro específico !!!!!!!Corrigir para gerente do terceiro!!!!!!!
        AND (
            -- Condição final: inclui apenas colaboradores que entraram APÓS a data de aplicação
            ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') > '2025-08-25'
        )
    )
)