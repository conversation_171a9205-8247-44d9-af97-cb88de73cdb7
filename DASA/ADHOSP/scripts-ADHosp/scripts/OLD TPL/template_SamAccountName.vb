'$SAMAccountName$ - trigger test for manual update
If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then
	Dim Acc As String = ""
	Dim MyString As String = ""
	Dim i As Integer = 0
	Dim AccExist As Boolean = False
	Dim txt As New System.Text.StringBuilder(Acc.Length)
	Dim f As ISqlFormatter = Connection.SqlFormatter
	Dim prefixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Prefix")
	Dim postfixAccount As String = connection.GetConfigParm("TargetSystem\ADS\Accounts\PrivilegedAccount\SAMAccountName_Postfix")

	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'do not get data from employee
			'only initially and if the operator has not set the SAMAccountName manually
			If Not $[IsLoaded]:Bool$ AndAlso $cn$<>"" AndAlso Not Entity.Columns.Item("SAMAccountName").ChangeLevels.Get(0) Then
				If $IsPrivilegedAccount:Bool$ Then
					Acc = Left(prefixAccount & Left($cn$, 18-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 18)
				Else
					Acc = Left($cn$, 18)
				End If
			Else
				Acc = CStr(Value)
			End If
		Case -1:'fill property initially depending on the employee
			If Not $[IsLoaded]:Bool$ Then
				If $IsPrivilegedAccount:Bool$ Then
					'Remove caracter inicial F ou T
					MyString = $FK(UID_Person).CentralAccount$
					Acc = Left(prefixAccount & Left(MyString.TrimStart("F"c,"T"c), 20-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 20)
				Else
					Acc = Left($FK(UID_Person).CentralAccount$,20) 
				End If
			Else
				Acc = CStr(Value)
			End If
		Case 1:'update property depending on the employee
			If $IsPrivilegedAccount:Bool$ Then
				MyString = $FK(UID_Person).CentralAccount$
				Acc = Left(prefixAccount & Left(MyString.TrimStart("F"c,"T"c), 20-prefixAccount.Length-postfixAccount.Length) & postfixAccount, 20)
			Else
				Acc = Left($FK(UID_Person).CentralAccount$,20) 
			End If
	End Select

	If Acc <> "" Then
		' Invalid characters in Samaccountname are replaced with _
		' /\[]:;|=,+*?<>"@
		For Each c As Char In Acc
			Select Case c
				Case "/"c, "\"c, "["c, "]"c, ":"c, ";"c, "|"c, "="c, ","c, "+"c, "*"c, "?"c, "<"c, ">"c, """"c, "@"c
					txt.Append("_"c)
				Case Else
					txt.Append(c)
			End Select
		Next
		Acc = txt.ToString()
		
		'character . at the end of Samaccountname is invalid
		If Right(Acc,1)="."c Then
			Acc = Acc.Substring(0,Acc.Length-1)
		End If
		
		'prevents triggering the template on changes to the ADSDomain (use ObjectWalker)
		Dim Domain As String = GetValue("UID_ADSDomain").String
			
		Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
			Case 0:'do not get data from employee (if this account is already in the domain then increment)
				Do While Connection.Exists("ADSAccount", f.AndRelation(f.Comparison("SAMAccountName", Acc, ValType.String, CompareOperator.Equal), _
					f.UidComparison("UID_ADSDomain", Domain), _
					f.UidComparison("UID_ADSAccount", $UID_ADSAccount$, CompareOperator.NotEqual)))
					i = i + 1
					Acc = txt.ToString() & i.ToString()
				Loop
		End Select

	End If
	
	Value = Acc

End If