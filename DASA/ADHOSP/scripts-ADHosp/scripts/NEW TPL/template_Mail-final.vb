' ========================================
' Template de Email - Segmentação DASA/IMPAR
' Autor: Sistema ADHosp
' Data: 2025-08-20
' Descrição: Gera endereços de email baseado na empresa (DASA/IMPAR)
' ========================================

If (Not CBool(Variables("FULLSYNC")) Or _
(CBool(Variables("FULLSYNC")) And ($FK(UID_Person).ImportSource$).StartsWith("SF_HR"))) Then

	Dim logMessage As String = ""

	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'Unmanaged (do not get data from employee)
			Try
				logMessage = String.Format("[{0}] Iniciando geração de email para conta: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), $SAMAccountName$)
				Console.WriteLine(logMessage)

				' Implementação da lógica de segmentação de email por empresa
				If $SAMAccountName$ <> "" Then
					Dim mailaddress As String = GerarEmailSegmentado()

					' Verificar unicidade do email
					If $Mail[o]$ <> mailaddress AndAlso mailaddress <> "" Then
						logMessage = String.Format("[{0}] Verificando unicidade do email: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
						Console.WriteLine(logMessage)

						Dim mailParts As String() = mailaddress.Split("@"c)
						Dim i As Integer = 0
						Dim f As ISqlFormatter = Connection.SqlFormatter
						Dim originalEmail As String = mailaddress

						While Connection.Exists("ADSAccount", _
							f.AndRelation( _
								f.UidComparison("UID_ADSAccount", $UID_ADSAccount$, CompareOperator.NotEqual), _
								f.Comparison("Mail", mailaddress, ValType.String, CompareOperator.Equal, FormatterOptions.None) _
							)) _
							OrElse Connection.Exists("ADSContact", _
								f.Comparison("Mail", mailaddress, ValType.String, CompareOperator.Equal, FormatterOptions.None) _
							)
							i += 1
							mailaddress = mailParts(0) & i.ToString() & "@" & mailParts(1)
							If mailaddress.Length > 255 Then
								mailaddress = mailParts(0).Substring(0, 255-i.ToString().Length) & i.ToString() & "@" & mailParts(1)
							End If

							If i = 100000 Then 'termination
								logMessage = String.Format("[{0}] ERRO: Limite de tentativas atingido para unicidade do email", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
								Console.WriteLine(logMessage)
								Exit While
							End If
						End While

						If originalEmail <> mailaddress Then
							logMessage = String.Format("[{0}] Email ajustado para unicidade: {1} -> {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), originalEmail, mailaddress)
							Console.WriteLine(logMessage)
						End If

						Value = mailaddress

						logMessage = String.Format("[{0}] Email final definido: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
						Console.WriteLine(logMessage)
					Else
						logMessage = String.Format("[{0}] Email não alterado ou vazio", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
						Console.WriteLine(logMessage)
					End If
				Else
					logMessage = String.Format("[{0}] SAMAccountName vazio, email não gerado", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
					Console.WriteLine(logMessage)
				End If

			Catch emailEx As Exception
				logMessage = String.Format("[{0}] ERRO na geração de email: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), emailEx.Message)
				Console.WriteLine(logMessage)
				' Manter valor original em caso de erro
			End Try

		Case -1:'fill property initially from the employee
			Try
				logMessage = String.Format("[{0}] Preenchimento inicial baseado no funcionário", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
				Console.WriteLine(logMessage)

				If Not $[IsLoaded]:Bool$ Then
					If $IsPrivilegedAccount:Bool$ Then
						Value = ""
						logMessage = String.Format("[{0}] Conta privilegiada - Email deixado vazio", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
						Console.WriteLine(logMessage)
					Else
						' Aplicar lógica de segmentação também no Case -1
						Value = GerarEmailSegmentado($FK(UID_ADSDomain).Ident_Domain$, $FK(UID_Person).DefaultEMailAddress$)
						logMessage = String.Format("[{0}] Email gerado com segmentação: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Value)
						Console.WriteLine(logMessage)
					End If
				End If
			Catch initEx As Exception
				logMessage = String.Format("[{0}] ERRO no preenchimento inicial: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), initEx.Message)
				Console.WriteLine(logMessage)
			End Try

		Case 1:'update property depending on employee
			Try
				logMessage = String.Format("[{0}] Atualização baseada no funcionário", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
				Console.WriteLine(logMessage)

				If $IsPrivilegedAccount:Bool$ Then
					Value = ""
					logMessage = String.Format("[{0}] Conta privilegiada - Email deixado vazio", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
					Console.WriteLine(logMessage)
				Else
					' Aplicar lógica de segmentação também no Case 1
					Value = GerarEmailSegmentado($FK(UID_ADSDomain).Ident_Domain$, $FK(UID_Person).DefaultEMailAddress$)
					logMessage = String.Format("[{0}] Email atualizado com segmentação: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Value)
					Console.WriteLine(logMessage)
				End If
			Catch updateEx As Exception
				logMessage = String.Format("[{0}] ERRO na atualização: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), updateEx.Message)
				Console.WriteLine(logMessage)
			End Try
	End Select

	logMessage = String.Format("[{0}] Processamento de email finalizado", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
	Console.WriteLine(logMessage)
End If

' ========================================
' Função para gerar email baseado na segmentação por empresa
' ========================================
Public Function GerarEmailSegmentado(dominioAD As String, enderecoPadraoEMail As String) As String
	Dim logMessage As String = ""
	Dim mailaddress As String = ""

	Try
		logMessage = String.Format("[{0}] Iniciando geração de email segmentado", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
		Console.WriteLine(logMessage)

		If enderecoPadraoEMail <> "" Then
			Dim usuario As String = enderecoPadraoEMail.ToLower.Split("@"c)(0)

			logMessage = String.Format("[{0}] Usuário identificado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), usuario)
			Console.WriteLine(logMessage)

			' Aplicar regras de segmentação de email
			logMessage = String.Format("[{0}] Aplicando regras de segmentação para domínio: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), dominioAD)
			Console.WriteLine(logMessage)

			If dominioAD = "adhosp" Then
				' Se for domínio IMPAR, usa sempre americasmed.com.br
				mailaddress = usuario + "@americasmed.com.br"

				logMessage = String.Format("[{0}] Regra IMPAR aplicada - Email gerado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
				Console.WriteLine(logMessage)

			ElseIf dominioAD = "DASA" Then
				' Se for domínio DASA, usa dasa.com.br, como já é hoje
				mailaddress = enderecoPadraoEMail

				logMessage = String.Format("[{0}] Regra DASA aplicada - Email gerado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
				Console.WriteLine(logMessage)

			Else
				' Caso não seja nenhuma das empresas conhecidas, usa domínio padrão
				logMessage = String.Format("[{0}] Domínio não reconhecido, aplicando regra padrão", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
				Console.WriteLine(logMessage)

				Dim maildom As String = Connection.GetConfigParm("QER\Person\DefaultMailDomain")
				If maildom <> "" Then
					If Not maildom.StartsWith("@") Then
						maildom = "@" & maildom
					End If
					mailaddress = usuario + maildom

					logMessage = String.Format("[{0}] Domínio padrão configurado usado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
					Console.WriteLine(logMessage)
				Else
					' Fallback se não houver configuração
					mailaddress = usuario + "@dominio-padrao.com.br"

					logMessage = String.Format("[{0}] AVISO: Usando fallback - Email gerado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
					Console.WriteLine(logMessage)
				End If
			End If

		Else
			' Se não houver SAMAccountName, usar email padrão da pessoa
			logMessage = String.Format("[{0}] SAMAccountName vazio, usando email padrão da pessoa", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
			Console.WriteLine(logMessage)

			mailaddress = $FK(UID_Person).DefaultEMailAddress$.ToLower

			logMessage = String.Format("[{0}] Email padrão obtido: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
			Console.WriteLine(logMessage)
		End If

	Catch ex As Exception
		logMessage = String.Format("[{0}] ERRO na geração de email segmentado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
		Console.WriteLine(logMessage)

		' Fallback para email padrão da pessoa
		Try
			mailaddress = $FK(UID_Person).DefaultEMailAddress$.ToLower
			logMessage = String.Format("[{0}] Fallback aplicado - Email: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), mailaddress)
			Console.WriteLine(logMessage)
		Catch fallbackEx As Exception
			logMessage = String.Format("[{0}] ERRO CRÍTICO no fallback: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), fallbackEx.Message)
			Console.WriteLine(logMessage)
			mailaddress = ""
		End Try
	End Try

	Return mailaddress
End Function